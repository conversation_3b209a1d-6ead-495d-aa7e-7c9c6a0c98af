-- 初始化数据库脚本
-- 创建数据库和用户已经在docker-compose.yml中通过环境变量配置

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 使用数据库
USE db_cloud_test;

-- 创建示例表（根据实际需要修改）
-- 这里只是示例，您需要根据实际的数据库结构来创建表

-- 用户表示例
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 插入默认管理员用户（密码需要根据实际加密方式处理）
INSERT INTO `users` (`username`, `password`, `email`, `status`) VALUES 
('admin', 'admin123', '<EMAIL>', 1)
ON DUPLICATE KEY UPDATE `username` = `username`;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
