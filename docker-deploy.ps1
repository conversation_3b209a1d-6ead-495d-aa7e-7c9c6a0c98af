# Manufacturing and Testing Management Platform Docker部署脚本
# PowerShell脚本用于Windows环境

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("build", "up", "down", "restart", "logs", "clean")]
    [string]$Action = "up"
)

Write-Host "Manufacturing and Testing Management Platform Docker部署脚本" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

function Build-Application {
    Write-Host "正在构建Spring Boot应用..." -ForegroundColor Yellow
    
    # 检查Maven是否可用
    if (!(Get-Command mvn -ErrorAction SilentlyContinue)) {
        Write-Host "错误: 未找到Maven命令，请确保Maven已安装并添加到PATH" -ForegroundColor Red
        exit 1
    }
    
    # 构建应用
    mvn clean package -DskipTests
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Maven构建失败" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Spring Boot应用构建完成" -ForegroundColor Green
}

function Start-Services {
    Write-Host "正在启动Docker服务..." -ForegroundColor Yellow
    
    # 检查Docker是否运行
    docker info | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: Docker未运行，请启动Docker Desktop" -ForegroundColor Red
        exit 1
    }
    
    # 构建并启动服务
    docker-compose up -d --build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "服务启动成功!" -ForegroundColor Green
        Write-Host "应用访问地址: http://localhost:8080" -ForegroundColor Cyan
        Write-Host "API文档地址: http://localhost:8080/swagger-ui.html" -ForegroundColor Cyan
        Write-Host "MySQL端口: 3306" -ForegroundColor Cyan
        Write-Host "Redis端口: 6379" -ForegroundColor Cyan
        Write-Host "FTP端口: 21" -ForegroundColor Cyan
    } else {
        Write-Host "服务启动失败" -ForegroundColor Red
        exit 1
    }
}

function Stop-Services {
    Write-Host "正在停止Docker服务..." -ForegroundColor Yellow
    docker-compose down
    Write-Host "服务已停止" -ForegroundColor Green
}

function Restart-Services {
    Write-Host "正在重启Docker服务..." -ForegroundColor Yellow
    docker-compose restart
    Write-Host "服务重启完成" -ForegroundColor Green
}

function Show-Logs {
    Write-Host "显示服务日志..." -ForegroundColor Yellow
    docker-compose logs -f
}

function Clean-Environment {
    Write-Host "正在清理Docker环境..." -ForegroundColor Yellow
    
    # 停止并删除容器
    docker-compose down -v
    
    # 删除镜像
    docker rmi massprdmanagement_service:latest -f 2>$null
    
    # 清理未使用的镜像和容器
    docker system prune -f
    
    Write-Host "环境清理完成" -ForegroundColor Green
}

# 主逻辑
switch ($Action) {
    "build" {
        Build-Application
    }
    "up" {
        Build-Application
        Start-Services
    }
    "down" {
        Stop-Services
    }
    "restart" {
        Restart-Services
    }
    "logs" {
        Show-Logs
    }
    "clean" {
        Clean-Environment
    }
    default {
        Write-Host "未知操作: $Action" -ForegroundColor Red
        Write-Host "可用操作: build, up, down, restart, logs, clean" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host "操作完成!" -ForegroundColor Green
