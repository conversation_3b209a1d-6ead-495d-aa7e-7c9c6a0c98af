# Maven
target/
!target/CloudTestServer-0.0.1-SNAPSHOT.jar
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE
.idea/
*.iws
*.iml
*.ipr
.vscode/
.settings/
.project
.classpath

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Other
README.md
*.md
.env
.env.local
.env.development
.env.test
.env.production

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Application specific
src/test/
*.properties
!application-docker.yml
