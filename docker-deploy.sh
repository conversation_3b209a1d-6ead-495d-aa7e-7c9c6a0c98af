#!/bin/bash

# Manufacturing and Testing Management Platform Docker部署脚本
# Bash脚本用于Linux/macOS环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${GREEN}Manufacturing and Testing Management Platform Docker部署脚本${NC}"
    echo -e "${GREEN}================================================${NC}"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build     - 仅构建Spring Boot应用"
    echo "  up        - 构建并启动所有服务 (默认)"
    echo "  down      - 停止所有服务"
    echo "  restart   - 重启所有服务"
    echo "  logs      - 显示服务日志"
    echo "  clean     - 清理Docker环境"
    echo "  help      - 显示此帮助信息"
}

# 构建应用
build_application() {
    echo -e "${YELLOW}正在构建Spring Boot应用...${NC}"
    
    # 检查Maven是否可用
    if ! command -v mvn &> /dev/null; then
        echo -e "${RED}错误: 未找到Maven命令，请确保Maven已安装并添加到PATH${NC}"
        exit 1
    fi
    
    # 构建应用
    mvn clean package -DskipTests
    
    echo -e "${GREEN}Spring Boot应用构建完成${NC}"
}

# 启动服务
start_services() {
    echo -e "${YELLOW}正在启动Docker服务...${NC}"
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        echo -e "${RED}错误: Docker未运行，请启动Docker服务${NC}"
        exit 1
    fi
    
    # 构建并启动服务
    docker-compose up -d --build
    
    echo -e "${GREEN}服务启动成功!${NC}"
    echo -e "${CYAN}应用访问地址: http://localhost:8080${NC}"
    echo -e "${CYAN}API文档地址: http://localhost:8080/swagger-ui.html${NC}"
    echo -e "${CYAN}MySQL端口: 3306${NC}"
    echo -e "${CYAN}Redis端口: 6379${NC}"
    echo -e "${CYAN}FTP端口: 21${NC}"
}

# 停止服务
stop_services() {
    echo -e "${YELLOW}正在停止Docker服务...${NC}"
    docker-compose down
    echo -e "${GREEN}服务已停止${NC}"
}

# 重启服务
restart_services() {
    echo -e "${YELLOW}正在重启Docker服务...${NC}"
    docker-compose restart
    echo -e "${GREEN}服务重启完成${NC}"
}

# 显示日志
show_logs() {
    echo -e "${YELLOW}显示服务日志...${NC}"
    docker-compose logs -f
}

# 清理环境
clean_environment() {
    echo -e "${YELLOW}正在清理Docker环境...${NC}"
    
    # 停止并删除容器
    docker-compose down -v
    
    # 删除镜像
    docker rmi massprdmanagement_service:latest -f 2>/dev/null || true
    
    # 清理未使用的镜像和容器
    docker system prune -f
    
    echo -e "${GREEN}环境清理完成${NC}"
}

# 主逻辑
ACTION=${1:-up}

case $ACTION in
    build)
        build_application
        ;;
    up)
        build_application
        start_services
        ;;
    down)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    logs)
        show_logs
        ;;
    clean)
        clean_environment
        ;;
    help)
        show_help
        ;;
    *)
        echo -e "${RED}未知操作: $ACTION${NC}"
        echo -e "${YELLOW}可用操作: build, up, down, restart, logs, clean, help${NC}"
        exit 1
        ;;
esac

echo -e "${GREEN}操作完成!${NC}"
