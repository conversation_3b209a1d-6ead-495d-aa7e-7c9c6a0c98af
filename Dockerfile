# 使用官方的OpenJDK 8运行时作为基础镜像
FROM openjdk:8-jre-alpine

# 设置镜像标签
LABEL name="MassPrdManageMent_Service" \
      version="1.0" \
      description="Manufacturing and Testing Management Platform - Cloud Test Server"

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/config /logs /u-test-2/MPTOOL_LOG_DATA

# 复制JAR文件到容器中
COPY target/CloudTestServer-0.0.1-SNAPSHOT.jar /app/CloudTestServer-0.0.1-SNAPSHOT.jar

# 复制配置文件
COPY application-docker.yml /app/config/application-docker.yml

# 设置时区
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 暴露端口
EXPOSE 8080

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom"

# 启动应用
CMD ["sh", "-c", "java $JAVA_OPTS -jar CloudTestServer-0.0.1-SNAPSHOT.jar --spring.config.additional-location=/app/config/application-docker.yml --spring.profiles.active=docker"]
