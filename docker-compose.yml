version: '3.8'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: massprd-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: db_cloud_test
      MYSQL_USER: db_cloud_test_user
      MYSQL_PASSWORD: db_cloud_test_pwd
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - massprd-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: massprd-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - massprd-network

  # FTP服务器
  ftp:
    image: stilliard/pure-ftpd
    container_name: massprd-ftp
    restart: unless-stopped
    ports:
      - "21:21"
      - "30000-30009:30000-30009"
    environment:
      PUBLICHOST: localhost
      FTP_USER_NAME: 古永浩
      FTP_USER_PASS: MIMA100dml
      FTP_USER_HOME: /home/<USER>
    volumes:
      - ftp_data:/home/<USER>
      - ftp_data:/ftp
    networks:
      - massprd-network

  # Manufacturing and Testing Management Platform Service
  massprd-service:
    build:
      context: .
      dockerfile: Dockerfile
    image: massprdmanagement_service:latest
    container_name: MassPrdManageMent_Service
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    volumes:
      - ./logs:/logs
      - ./docker/app-data:/u-test-2/MPTOOL_LOG_DATA
    depends_on:
      - mysql
      - redis
      - ftp
    networks:
      - massprd-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# 网络配置
networks:
  massprd-network:
    driver: bridge

# 数据卷配置
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  ftp_data:
    driver: local
