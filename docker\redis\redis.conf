# Redis配置文件

# 网络配置
bind 0.0.0.0
port 6379
timeout 300

# 通用配置
daemonize no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""

# 数据库配置
databases 16
save 900 1
save 300 10
save 60 10000

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# 安全配置（Docker环境暂不设置密码）
# requirepass your_password_here

# 客户端配置
tcp-keepalive 300
tcp-backlog 511
