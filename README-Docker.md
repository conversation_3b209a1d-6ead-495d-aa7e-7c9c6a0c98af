# Manufacturing and Testing Management Platform - Docker部署指南

## 概述

本项目已配置为使用Docker容器化部署，包含以下服务：
- **MassPrdManageMent_Service**: Spring Boot应用主服务
- **MySQL 8.0**: 数据库服务
- **Redis 7**: 缓存服务  
- **Pure-FTPd**: FTP文件服务器

## 前置要求

1. **Docker Desktop** (Windows/macOS) 或 **Docker Engine** (Linux)
2. **Docker Compose** (通常随Docker Desktop一起安装)
3. **Maven 3.6+** (用于构建Java应用)
4. **Java 8+** (用于本地开发，容器内已包含)

## 快速开始

### Windows环境

```powershell
# 构建并启动所有服务
.\docker-deploy.ps1

# 或者指定具体操作
.\docker-deploy.ps1 -Action up
```

### Linux/macOS环境

```bash
# 给脚本执行权限
chmod +x docker-deploy.sh

# 构建并启动所有服务
./docker-deploy.sh

# 或者指定具体操作
./docker-deploy.sh up
```

### 手动部署

```bash
# 1. 构建Spring Boot应用
mvn clean package -DskipTests

# 2. 启动Docker服务
docker-compose up -d --build
```

## 服务访问

启动成功后，可以通过以下地址访问服务：

- **应用主页**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html
- **健康检查**: http://localhost:8080/actuator/health
- **MySQL**: localhost:3306
- **Redis**: localhost:6379
- **FTP**: localhost:21

## 管理命令

### PowerShell脚本 (Windows)

```powershell
# 构建应用
.\docker-deploy.ps1 -Action build

# 启动服务
.\docker-deploy.ps1 -Action up

# 停止服务
.\docker-deploy.ps1 -Action down

# 重启服务
.\docker-deploy.ps1 -Action restart

# 查看日志
.\docker-deploy.ps1 -Action logs

# 清理环境
.\docker-deploy.ps1 -Action clean
```

### Bash脚本 (Linux/macOS)

```bash
# 构建应用
./docker-deploy.sh build

# 启动服务
./docker-deploy.sh up

# 停止服务
./docker-deploy.sh down

# 重启服务
./docker-deploy.sh restart

# 查看日志
./docker-deploy.sh logs

# 清理环境
./docker-deploy.sh clean
```

### Docker Compose命令

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启特定服务
docker-compose restart massprd-service
```

## 配置说明

### 环境配置

- **开发环境**: `application-test-local.yml`
- **Docker环境**: `application-docker.yml`

### 数据库配置

- **数据库名**: db_cloud_test
- **用户名**: db_cloud_test_user
- **密码**: db_cloud_test_pwd
- **初始化脚本**: `docker/mysql/init/01-init-database.sql`

### FTP配置

- **用户名**: 古永浩
- **密码**: MIMA100dml
- **端口**: 21, 30000-30009

## 数据持久化

以下数据将持久化存储：
- MySQL数据: `mysql_data` volume
- Redis数据: `redis_data` volume  
- FTP数据: `ftp_data` volume
- 应用日志: `./logs` 目录
- MP日志数据: `./docker/app-data` 目录

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -an | findstr :8080
   # 或修改docker-compose.yml中的端口映射
   ```

2. **Maven构建失败**
   ```bash
   # 清理Maven缓存
   mvn clean
   # 检查网络连接和Maven配置
   ```

3. **Docker服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs massprd-service
   # 检查Docker Desktop是否运行
   ```

4. **数据库连接失败**
   ```bash
   # 检查MySQL容器状态
   docker-compose ps mysql
   # 查看MySQL日志
   docker-compose logs mysql
   ```

### 重置环境

如果遇到严重问题，可以完全重置环境：

```bash
# 停止并删除所有容器和数据
docker-compose down -v

# 删除镜像
docker rmi massprdmanagement_service:latest

# 清理Docker系统
docker system prune -a

# 重新构建和启动
./docker-deploy.sh up
```

## 开发建议

1. **本地开发**: 使用 `application-test-local.yml` 配置
2. **容器测试**: 使用 `application-docker.yml` 配置
3. **日志查看**: 容器日志在 `./logs` 目录下
4. **数据备份**: 定期备份 Docker volumes 中的数据

## 生产部署注意事项

1. 修改默认密码（数据库、Redis、FTP）
2. 配置SSL/TLS证书
3. 设置防火墙规则
4. 配置监控和日志收集
5. 设置数据备份策略
